<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Quotation - HCLSoftware</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #1a1a1a;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header Styles */
        .header {
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            color: white;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo-text {
            font-size: 22px;
            font-weight: 800;
            letter-spacing: -0.8px;
            color: white;
        }

        .search-container {
            position: relative;
        }

        .search-input {
            width: 350px;
            padding: 12px 48px 12px 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            color: white;
            font-size: 14px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.8);
        }

        .search-input:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.4);
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .search-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.7);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .welcome-text {
            font-size: 13px;
            font-weight: 400;
        }

        .help-icon {
            font-size: 16px;
            cursor: pointer;
            opacity: 0.8;
            transition: opacity 0.3s ease;
        }

        .help-icon:hover {
            opacity: 1;
        }

        /* Main Container */
        .main-container {
            display: flex;
            min-height: calc(100vh - 60px);
        }

        /* Sidebar Styles */
        .sidebar {
            width: 280px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(0, 0, 0, 0.1);
            overflow-y: auto;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
            margin: 20px 0 20px 20px;
            border-radius: 16px;
        }

        .nav-section {
            padding: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.02));
            border-radius: 16px 16px 0 0;
        }

        .nav-item {
            padding: 12px 16px;
            margin: 4px 0;
            border-radius: 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            color: #4a4a4a;
            position: relative;
            overflow: hidden;
        }

        .nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
            transition: left 0.5s;
        }

        .nav-item:hover::before {
            left: 100%;
        }

        .nav-item:hover {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.02));
            color: #1a1a1a;
            transform: translateX(4px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .nav-item.active {
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            color: white;
            transform: translateX(8px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .nav-menu {
            padding: 10px 0;
        }

        .nav-group {
            margin: 2px 0;
        }

        .nav-group-header {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 14px 20px;
            cursor: pointer;
            font-size: 11px;
            font-weight: 700;
            color: #6a6a6a;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 8px;
            margin: 2px 8px;
        }

        .nav-group-header:hover {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.02));
            color: #2a2a2a;
            transform: translateX(2px);
        }

        .nav-group-header i {
            font-size: 10px;
            transition: transform 0.3s ease;
        }

        /* Content Styles */
        .content {
            flex: 1;
            padding: 32px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            margin: 20px 20px 20px 0;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.2), transparent);
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            position: relative;
        }

        .content-title h1 {
            font-size: 32px;
            font-weight: 700;
            color: #1a1a1a;
            letter-spacing: -1px;
        }

        /* Button Styles */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            color: white;
            border: 1px solid transparent;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            background: linear-gradient(135deg, #000000, #1a1a1a);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            color: #4a4a4a;
            border: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 1);
            color: #1a1a1a;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: rgba(0, 0, 0, 0.2);
        }

        /* Action Bar */
        .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        }

        .action-left {
            display: flex;
            gap: 10px;
        }

        /* Table Styles */
        .table-container {
            overflow-x: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            margin-bottom: 24px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: transparent;
        }

        .data-table th {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.02));
            padding: 16px 12px;
            text-align: left;
            font-weight: 700;
            font-size: 12px;
            color: #1a1a1a;
            border-bottom: 2px solid rgba(0, 0, 0, 0.1);
            white-space: nowrap;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .data-table td {
            padding: 16px 12px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            font-size: 13px;
            color: #4a4a4a;
            transition: all 0.3s ease;
        }

        .data-table tr {
            transition: all 0.3s ease;
        }

        .data-table tr:hover {
            background: rgba(0, 0, 0, 0.02);
            transform: scale(1.01);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .view-icon {
            color: #6a6a6a;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 8px;
            border-radius: 8px;
        }

        .view-icon:hover {
            color: #1a1a1a;
            background: rgba(0, 0, 0, 0.05);
            transform: scale(1.1);
        }

        .quotation-link {
            color: #1a1a1a;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            padding: 4px 8px;
            border-radius: 6px;
        }

        .quotation-link:hover {
            background: rgba(0, 0, 0, 0.05);
            color: #000000;
            transform: translateX(2px);
        }

        /* Status Styles */
        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .status.approved {
            background: linear-gradient(135deg, #2d3748, #1a202c);
            color: white;
            border: 1px solid #2d3748;
        }

        .status.pending {
            background: linear-gradient(135deg, #e2e8f0, #cbd5e0);
            color: #2d3748;
            border: 1px solid #a0aec0;
        }

        /* Pagination */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            margin-top: 20px;
        }

        .pagination-info {
            font-size: 14px;
            color: #4a4a4a;
            font-weight: 500;
        }

        .pagination-controls {
            display: flex;
            gap: 8px;
        }

        .pagination-btn {
            width: 40px;
            height: 40px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            color: #4a4a4a;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .pagination-btn:hover:not(:disabled) {
            background: rgba(0, 0, 0, 0.05);
            border-color: rgba(0, 0, 0, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
            color: #1a1a1a;
        }

        .pagination-btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .sidebar {
                width: 200px;
            }

            .search-input {
                width: 200px;
            }
        }

        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
            }

            .header {
                flex-direction: column;
                gap: 10px;
                padding: 15px;
            }

            .header-left,
            .header-right {
                width: 100%;
                justify-content: center;
            }

            .search-input {
                width: 100%;
                max-width: 300px;
            }

            .content-header {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }

            .action-bar {
                flex-direction: column;
                gap: 15px;
            }

            .action-left {
                flex-wrap: wrap;
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .content {
                margin: 10px;
                padding: 15px;
            }

            .data-table {
                font-size: 11px;
            }

            .data-table th,
            .data-table td {
                padding: 8px 4px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-left">
            <div class="logo">
                <span class="logo-text">HCLSoftware</span>
            </div>
            <div class="search-container">
                <input type="search" class="search-input" placeholder="Search...">
                <i class="fas fa-search search-icon"></i>
            </div>
        </div>
        <div class="header-right">
            <span class="welcome-text">Welcome - Admin - <strong>Branch 11</strong></span>
            <i class="fas fa-question-circle help-icon"></i>
        </div>
    </header>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="nav-section">
                <div class="nav-item active">
                    <span class="nav-text">Service Invoice Return</span>
                </div>
                <div class="nav-item">
                    <span class="nav-text">Service Type</span>
                </div>
                <div class="nav-item">
                    <span class="nav-text">Movement Type</span>
                </div>
            </div>

            <div class="nav-menu">
                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>HELPDESK</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>EMAILS</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>SERVICE</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>ITEMS</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>SCHEDULER</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>CRM</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>DASHBOARD</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>JOB RECEIPT</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>CONTRACT MANAGEMENT</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>DIGITAL CATALOGUE</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>REPORTS</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>ORDER MANAGEMENT</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>FIELD SERVICE</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>WARRANTY</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>SALES</span>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="content">
            <!-- Content Header -->
            <div class="content-header">
                <div class="content-title">
                    <h1>Service Quotation</h1>
                </div>
                <div class="content-actions">
                    <button class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        New
                    </button>
                </div>
            </div>

            <!-- Action Bar -->
            <div class="action-bar">
                <div class="action-left">
                    <button class="btn btn-secondary">
                        <i class="fas fa-search"></i>
                        Advanced Search
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-download"></i>
                        Export
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-refresh"></i>
                        Refresh
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-info-circle"></i>
                        Legend
                    </button>
                </div>
            </div>

            <!-- Data Table -->
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>View</th>
                            <th>Quotation #</th>
                            <th>Ver</th>
                            <th>Customer #</th>
                            <th>Name</th>
                            <th>Customer Complaint</th>
                            <th>Date</th>
                            <th>Model</th>
                            <th>Unit #</th>
                            <th>Serial #</th>
                            <th>Work Order #</th>
                            <th>Ticket #</th>
                            <th>Ticket Date</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><i class="fas fa-eye view-icon"></i></td>
                            <td class="quotation-link">QOT17N/2025-1</td>
                            <td>1</td>
                            <td>0100022087</td>
                            <td>Customer 1487</td>
                            <td>Axis replacement</td>
                            <td>26-Dec-2024</td>
                            <td>Model 30</td>
                            <td></td>
                            <td>Serial3898</td>
                            <td>WO17N/2025-1</td>
                            <td></td>
                            <td></td>
                            <td><span class="status approved">Approved</span></td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-eye view-icon"></i></td>
                            <td class="quotation-link">QOT17N/2024-1</td>
                            <td>1</td>
                            <td>0100024564</td>
                            <td>Customer 1673</td>
                            <td>Gear box replacement</td>
                            <td>24-Nov-2024</td>
                            <td>Model 36</td>
                            <td></td>
                            <td>Serial9911</td>
                            <td>WO17N/2024-1</td>
                            <td></td>
                            <td></td>
                            <td><span class="status approved">Approved</span></td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-eye view-icon"></i></td>
                            <td class="quotation-link">QOT17N/2024-1</td>
                            <td>1</td>
                            <td>0100024563</td>
                            <td>Customer 156-90</td>
                            <td>Oil</td>
                            <td>24-Nov-2024</td>
                            <td>Model 35</td>
                            <td></td>
                            <td>Serial5531</td>
                            <td></td>
                            <td>TKT7N/2024-1</td>
                            <td>25-Dec-2024</td>
                            <td><span class="status approved">Approved</span></td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-eye view-icon"></i></td>
                            <td class="quotation-link">QOT17N/2024-1</td>
                            <td>1</td>
                            <td>0100017358</td>
                            <td>Customer 5791</td>
                            <td>Fire testing like yesterday</td>
                            <td>25-Dec-2024</td>
                            <td>Model 18</td>
                            <td>SN147</td>
                            <td>Serial33968</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td><span class="status pending">Pending for C</span></td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-eye view-icon"></i></td>
                            <td class="quotation-link">QOT17N/2024-4</td>
                            <td>1</td>
                            <td>0100024549</td>
                            <td>Customer 156-90</td>
                            <td>test</td>
                            <td>21-Dec-2024</td>
                            <td>Model 16</td>
                            <td></td>
                            <td>Serial20940</td>
                            <td>TKT7N/2024-4</td>
                            <td>21-Dec-2024</td>
                            <td><span class="status pending">Pending for C</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="pagination-container">
                <div class="pagination-info">
                    <span>1 of 1 of 5</span>
                </div>
                <div class="pagination-controls">
                    <button class="pagination-btn" disabled>
                        <i class="fas fa-angle-double-left"></i>
                    </button>
                    <button class="pagination-btn" disabled>
                        <i class="fas fa-angle-left"></i>
                    </button>
                    <button class="pagination-btn" disabled>
                        <i class="fas fa-angle-right"></i>
                    </button>
                    <button class="pagination-btn" disabled>
                        <i class="fas fa-angle-double-right"></i>
                    </button>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
