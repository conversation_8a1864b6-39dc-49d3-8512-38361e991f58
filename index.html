<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Quotation - HCLSoftware</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.5;
            color: #333;
            background-color: #f8f9fa;
        }

        /* Header Styles */
        .header {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            color: white;
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo-text {
            font-size: 18px;
            font-weight: 700;
            letter-spacing: -0.5px;
        }

        .search-container {
            position: relative;
        }

        .search-input {
            width: 300px;
            padding: 8px 40px 8px 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .search-input:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .search-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.7);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .welcome-text {
            font-size: 13px;
            font-weight: 400;
        }

        .help-icon {
            font-size: 16px;
            cursor: pointer;
            opacity: 0.8;
            transition: opacity 0.3s ease;
        }

        .help-icon:hover {
            opacity: 1;
        }

        /* Main Container */
        .main-container {
            display: flex;
            min-height: calc(100vh - 60px);
        }

        /* Sidebar Styles */
        .sidebar {
            width: 250px;
            background: white;
            border-right: 1px solid #e5e7eb;
            overflow-y: auto;
            box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05);
        }

        .nav-section {
            padding: 15px;
            border-bottom: 1px solid #e5e7eb;
            background: #f8f9fa;
        }

        .nav-item {
            padding: 8px 12px;
            margin: 2px 0;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-item:hover {
            background: #e5e7eb;
        }

        .nav-item.active {
            background: #3b82f6;
            color: white;
        }

        .nav-menu {
            padding: 10px 0;
        }

        .nav-group {
            margin: 2px 0;
        }

        .nav-group-header {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .nav-group-header:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .nav-group-header i {
            font-size: 10px;
            transition: transform 0.3s ease;
        }

        /* Content Styles */
        .content {
            flex: 1;
            padding: 20px;
            background: white;
            margin: 15px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e5e7eb;
        }

        .content-title h1 {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
        }

        /* Button Styles */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
            border-color: #9ca3af;
        }

        /* Action Bar */
        .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .action-left {
            display: flex;
            gap: 10px;
        }

        /* Table Styles */
        .table-container {
            overflow-x: auto;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        .data-table th {
            background: #f8f9fa;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            font-size: 12px;
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
            white-space: nowrap;
        }

        .data-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #f3f4f6;
            font-size: 12px;
            color: #4b5563;
        }

        .data-table tr:hover {
            background: #f8f9fa;
        }

        .view-icon {
            color: #6b7280;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .view-icon:hover {
            color: #3b82f6;
        }

        .quotation-link {
            color: #3b82f6;
            cursor: pointer;
            font-weight: 500;
        }

        .quotation-link:hover {
            text-decoration: underline;
        }

        /* Status Styles */
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status.approved {
            background: #dcfce7;
            color: #166534;
        }

        .status.pending {
            background: #fef3c7;
            color: #92400e;
        }

        /* Pagination */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-top: 1px solid #e5e7eb;
        }

        .pagination-info {
            font-size: 13px;
            color: #6b7280;
        }

        .pagination-controls {
            display: flex;
            gap: 5px;
        }

        .pagination-btn {
            width: 32px;
            height: 32px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pagination-btn:hover:not(:disabled) {
            background: #f3f4f6;
            border-color: #9ca3af;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .sidebar {
                width: 200px;
            }

            .search-input {
                width: 200px;
            }
        }

        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
            }

            .header {
                flex-direction: column;
                gap: 10px;
                padding: 15px;
            }

            .header-left,
            .header-right {
                width: 100%;
                justify-content: center;
            }

            .search-input {
                width: 100%;
                max-width: 300px;
            }

            .content-header {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }

            .action-bar {
                flex-direction: column;
                gap: 15px;
            }

            .action-left {
                flex-wrap: wrap;
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .content {
                margin: 10px;
                padding: 15px;
            }

            .data-table {
                font-size: 11px;
            }

            .data-table th,
            .data-table td {
                padding: 8px 4px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-left">
            <div class="logo">
                <span class="logo-text">HCLSoftware</span>
            </div>
            <div class="search-container">
                <input type="search" class="search-input" placeholder="Search...">
                <i class="fas fa-search search-icon"></i>
            </div>
        </div>
        <div class="header-right">
            <span class="welcome-text">Welcome - Admin - <strong>Branch 11</strong></span>
            <i class="fas fa-question-circle help-icon"></i>
        </div>
    </header>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="nav-section">
                <div class="nav-item active">
                    <span class="nav-text">Service Invoice Return</span>
                </div>
                <div class="nav-item">
                    <span class="nav-text">Service Type</span>
                </div>
                <div class="nav-item">
                    <span class="nav-text">Movement Type</span>
                </div>
            </div>

            <div class="nav-menu">
                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>HELPDESK</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>EMAILS</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>SERVICE</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>ITEMS</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>SCHEDULER</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>CRM</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>DASHBOARD</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>JOB RECEIPT</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>CONTRACT MANAGEMENT</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>DIGITAL CATALOGUE</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>REPORTS</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>ORDER MANAGEMENT</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>FIELD SERVICE</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>WARRANTY</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>SALES</span>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="content">
            <!-- Content Header -->
            <div class="content-header">
                <div class="content-title">
                    <h1>Service Quotation</h1>
                </div>
                <div class="content-actions">
                    <button class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        New
                    </button>
                </div>
            </div>

            <!-- Action Bar -->
            <div class="action-bar">
                <div class="action-left">
                    <button class="btn btn-secondary">
                        <i class="fas fa-search"></i>
                        Advanced Search
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-download"></i>
                        Export
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-refresh"></i>
                        Refresh
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-info-circle"></i>
                        Legend
                    </button>
                </div>
            </div>

            <!-- Data Table -->
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>View</th>
                            <th>Quotation #</th>
                            <th>Ver</th>
                            <th>Customer #</th>
                            <th>Name</th>
                            <th>Customer Complaint</th>
                            <th>Date</th>
                            <th>Model</th>
                            <th>Unit #</th>
                            <th>Serial #</th>
                            <th>Work Order #</th>
                            <th>Ticket #</th>
                            <th>Ticket Date</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><i class="fas fa-eye view-icon"></i></td>
                            <td class="quotation-link">QOT17N/2025-1</td>
                            <td>1</td>
                            <td>0100022087</td>
                            <td>Customer 1487</td>
                            <td>Axis replacement</td>
                            <td>26-Dec-2024</td>
                            <td>Model 30</td>
                            <td></td>
                            <td>Serial3898</td>
                            <td>WO17N/2025-1</td>
                            <td></td>
                            <td></td>
                            <td><span class="status approved">Approved</span></td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-eye view-icon"></i></td>
                            <td class="quotation-link">QOT17N/2024-1</td>
                            <td>1</td>
                            <td>0100024564</td>
                            <td>Customer 1673</td>
                            <td>Gear box replacement</td>
                            <td>24-Nov-2024</td>
                            <td>Model 36</td>
                            <td></td>
                            <td>Serial9911</td>
                            <td>WO17N/2024-1</td>
                            <td></td>
                            <td></td>
                            <td><span class="status approved">Approved</span></td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-eye view-icon"></i></td>
                            <td class="quotation-link">QOT17N/2024-1</td>
                            <td>1</td>
                            <td>0100024563</td>
                            <td>Customer 156-90</td>
                            <td>Oil</td>
                            <td>24-Nov-2024</td>
                            <td>Model 35</td>
                            <td></td>
                            <td>Serial5531</td>
                            <td></td>
                            <td>TKT7N/2024-1</td>
                            <td>25-Dec-2024</td>
                            <td><span class="status approved">Approved</span></td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-eye view-icon"></i></td>
                            <td class="quotation-link">QOT17N/2024-1</td>
                            <td>1</td>
                            <td>0100017358</td>
                            <td>Customer 5791</td>
                            <td>Fire testing like yesterday</td>
                            <td>25-Dec-2024</td>
                            <td>Model 18</td>
                            <td>SN147</td>
                            <td>Serial33968</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td><span class="status pending">Pending for C</span></td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-eye view-icon"></i></td>
                            <td class="quotation-link">QOT17N/2024-4</td>
                            <td>1</td>
                            <td>0100024549</td>
                            <td>Customer 156-90</td>
                            <td>test</td>
                            <td>21-Dec-2024</td>
                            <td>Model 16</td>
                            <td></td>
                            <td>Serial20940</td>
                            <td>TKT7N/2024-4</td>
                            <td>21-Dec-2024</td>
                            <td><span class="status pending">Pending for C</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="pagination-container">
                <div class="pagination-info">
                    <span>1 of 1 of 5</span>
                </div>
                <div class="pagination-controls">
                    <button class="pagination-btn" disabled>
                        <i class="fas fa-angle-double-left"></i>
                    </button>
                    <button class="pagination-btn" disabled>
                        <i class="fas fa-angle-left"></i>
                    </button>
                    <button class="pagination-btn" disabled>
                        <i class="fas fa-angle-right"></i>
                    </button>
                    <button class="pagination-btn" disabled>
                        <i class="fas fa-angle-double-right"></i>
                    </button>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
