<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Quotation - HCLSoftware</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #1a1a1a;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header Styles */
        .header {
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            color: white;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo-text {
            font-size: 22px;
            font-weight: 800;
            letter-spacing: -0.8px;
            color: white;
        }

        .search-container {
            position: relative;
        }

        .search-input {
            width: 350px;
            padding: 12px 48px 12px 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            color: white;
            font-size: 14px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.8);
        }

        .search-input:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.4);
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .search-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.7);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .welcome-text {
            font-size: 13px;
            font-weight: 400;
        }

        .help-icon {
            font-size: 16px;
            cursor: pointer;
            opacity: 0.8;
            transition: opacity 0.3s ease;
        }

        .help-icon:hover {
            opacity: 1;
        }

        /* Main Container */
        .main-container {
            display: flex;
            min-height: calc(100vh - 60px);
        }

        /* Sidebar Styles */
        .sidebar {
            width: 280px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(0, 0, 0, 0.1);
            overflow-y: auto;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
            margin: 20px 0 20px 20px;
            border-radius: 16px;
        }

        .nav-section {
            padding: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.02));
            border-radius: 16px 16px 0 0;
        }

        .nav-item {
            padding: 12px 16px;
            margin: 4px 0;
            border-radius: 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            color: #4a4a4a;
            position: relative;
            overflow: hidden;
        }

        .nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
            transition: left 0.5s;
        }

        .nav-item:hover::before {
            left: 100%;
        }

        .nav-item:hover {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.02));
            color: #1a1a1a;
            transform: translateX(4px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .nav-item.active {
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            color: white;
            transform: translateX(8px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .nav-menu {
            padding: 10px 0;
        }

        .nav-group {
            margin: 2px 0;
        }

        .nav-group-header {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 14px 20px;
            cursor: pointer;
            font-size: 11px;
            font-weight: 700;
            color: #6a6a6a;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 8px;
            margin: 2px 8px;
        }

        .nav-group-header:hover {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.02));
            color: #2a2a2a;
            transform: translateX(2px);
        }

        .nav-group-header i {
            font-size: 10px;
            transition: transform 0.3s ease;
        }

        /* Content Styles */
        .content {
            flex: 1;
            padding: 32px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            margin: 20px 20px 20px 0;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.2), transparent);
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            position: relative;
        }

        .content-title h1 {
            font-size: 32px;
            font-weight: 700;
            color: #1a1a1a;
            letter-spacing: -1px;
        }

        /* Button Styles */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            color: white;
            border: 1px solid transparent;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            background: linear-gradient(135deg, #000000, #1a1a1a);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            color: #4a4a4a;
            border: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 1);
            color: #1a1a1a;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: rgba(0, 0, 0, 0.2);
        }

        /* Action Bar */
        .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        }

        .action-left {
            display: flex;
            gap: 10px;
        }

        /* Table Styles */
        .table-container {
            overflow-x: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            margin-bottom: 24px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: transparent;
        }

        .data-table th {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.02));
            padding: 16px 12px;
            text-align: left;
            font-weight: 700;
            font-size: 12px;
            color: #1a1a1a;
            border-bottom: 2px solid rgba(0, 0, 0, 0.1);
            white-space: nowrap;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .data-table td {
            padding: 16px 12px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            font-size: 13px;
            color: #4a4a4a;
            transition: all 0.3s ease;
        }

        .data-table tr {
            transition: all 0.3s ease;
        }

        .data-table tr:hover {
            background: rgba(0, 0, 0, 0.02);
            transform: scale(1.01);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .view-icon {
            color: #6a6a6a;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 8px;
            border-radius: 8px;
        }

        .view-icon:hover {
            color: #1a1a1a;
            background: rgba(0, 0, 0, 0.05);
            transform: scale(1.1);
        }

        .quotation-link {
            color: #1a1a1a;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            padding: 4px 8px;
            border-radius: 6px;
        }

        .quotation-link:hover {
            background: rgba(0, 0, 0, 0.05);
            color: #000000;
            transform: translateX(2px);
        }

        /* Status Styles */
        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .status.approved {
            background: linear-gradient(135deg, #2d3748, #1a202c);
            color: white;
            border: 1px solid #2d3748;
        }

        .status.pending {
            background: linear-gradient(135deg, #e2e8f0, #cbd5e0);
            color: #2d3748;
            border: 1px solid #a0aec0;
        }

        /* Pagination */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            margin-top: 20px;
        }

        .pagination-info {
            font-size: 14px;
            color: #4a4a4a;
            font-weight: 500;
        }

        .pagination-controls {
            display: flex;
            gap: 8px;
        }

        .pagination-btn {
            width: 40px;
            height: 40px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            color: #4a4a4a;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .pagination-btn:hover:not(:disabled) {
            background: rgba(0, 0, 0, 0.05);
            border-color: rgba(0, 0, 0, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
            color: #1a1a1a;
        }

        .pagination-btn:disabled {
            opacity: 0.4;
            cursor: not-allowed;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(5px);
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .modal-content {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            width: 90%;
            max-width: 1200px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: modalSlideIn 0.3s ease-out;
            transition: all 0.3s ease;
        }

        .modal-content.with-ticket-panel {
            width: 90%;
            max-width: 1200px;
            transform: translateX(-25%);
            margin-left: 20px;
            margin-right: 0;
            justify-self: flex-start;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Separate Ticket Panel */
        .ticket-panel-modal {
            position: fixed;
            top: 5vh;
            right: 20px;
            width: 40%;
            max-width: 500px;
            height: 90vh;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 2001;
            display: none;
            animation: slideInFromRight 0.3s ease-out;
            overflow: hidden;
        }

        @keyframes slideInFromRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.02), rgba(0, 0, 0, 0.01));
            border-radius: 16px 16px 0 0;
        }

        .modal-header h2 {
            font-size: 20px;
            font-weight: 700;
            color: #1a1a1a;
            margin: 0;
        }

        .modal-actions {
            display: flex;
            gap: 8px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 11px;
        }

        .modal-body {
            padding: 20px;
            max-height: 70vh;
            overflow-y: auto;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 8px;
            padding: 16px 20px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 0.02));
            border-radius: 0 0 16px 16px;
        }

        /* Form Styles */
        .form-section {
            margin-bottom: 20px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            border: 1px solid rgba(0, 0, 0, 0.05);
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
        }

        .section-header {
            padding: 12px 16px;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.03), rgba(0, 0, 0, 0.01));
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .section-header:hover {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.02));
        }

        .section-header.collapsible {
            user-select: none;
        }

        .section-header h3 {
            font-size: 14px;
            font-weight: 600;
            color: #1a1a1a;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .section-content {
            padding: 16px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 12px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-size: 11px;
            font-weight: 600;
            color: #2a2a2a;
            margin-bottom: 4px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .form-input,
        .form-select,
        .form-textarea {
            padding: 8px 12px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 6px;
            font-size: 13px;
            color: #1a1a1a;
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        .form-input[readonly] {
            background: rgba(0, 0, 0, 0.05);
            color: #6a6a6a;
        }

        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }

        /* Component Tabs */
        .component-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            margin-bottom: 24px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            padding-bottom: 16px;
        }

        .tab-btn {
            padding: 10px 16px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            background: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            font-size: 12px;
            font-weight: 500;
            color: #4a4a4a;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .tab-btn:hover {
            background: rgba(0, 0, 0, 0.05);
            color: #1a1a1a;
        }

        .tab-btn.active {
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            color: white;
            border-color: #1a1a1a;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Status Legend */
        .status-legend {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            padding: 16px;
            background: rgba(0, 0, 0, 0.02);
            border-radius: 8px;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            font-weight: 500;
            color: #4a4a4a;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }

        /* Component Table */
        .component-table-container {
            overflow-x: auto;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.9);
        }

        .component-table {
            width: 100%;
            border-collapse: collapse;
            min-width: 800px;
        }

        .component-table th {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.02));
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            font-size: 11px;
            color: #1a1a1a;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            white-space: nowrap;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .component-table td {
            padding: 8px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .table-input,
        .table-select {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            font-size: 12px;
            background: rgba(255, 255, 255, 0.9);
            color: #1a1a1a;
        }

        .table-input:focus,
        .table-select:focus {
            outline: none;
            border-color: rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 1);
        }

        /* Top Form Row Styles */
        .top-form-row {
            display: grid;
            grid-template-columns: auto 1fr 1fr 1fr;
            gap: 12px;
            padding: 16px;
            align-items: end;
        }

        .second-form-row {
            display: grid;
            grid-template-columns: 1fr 2fr auto;
            gap: 12px;
            padding: 0 16px 16px 16px;
            align-items: end;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-top: 20px;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            color: #1a1a1a;
        }

        .form-checkbox {
            width: 18px;
            height: 18px;
            accent-color: #1a1a1a;
            cursor: pointer;
        }

        .checkbox-text {
            user-select: none;
        }

        .ticket-input-container {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .search-ticket-btn {
            padding: 12px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.9);
            color: #4a4a4a;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 44px;
        }

        .search-ticket-btn:hover {
            background: rgba(0, 0, 0, 0.05);
            color: #1a1a1a;
            transform: translateY(-1px);
        }

        /* Ticket Modal Styles */
        .ticket-modal .modal-content {
            max-width: 1000px;
            width: 95%;
        }

        .ticket-modal-content {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            width: 100%;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: modalSlideIn 0.3s ease-out;
        }

        .ticket-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24px 32px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.02), rgba(0, 0, 0, 0.01));
        }

        .ticket-modal-header h2 {
            font-size: 24px;
            font-weight: 700;
            color: #1a1a1a;
            margin: 0;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            color: #6a6a6a;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(0, 0, 0, 0.05);
            color: #1a1a1a;
        }

        .ticket-modal-body {
            padding: 32px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .ticket-modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            padding: 24px 32px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 0.02));
        }

        .ticket-search-bar {
            position: relative;
            margin-bottom: 24px;
        }

        .search-input-modal {
            width: 100%;
            padding: 12px 48px 12px 16px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            font-size: 14px;
            color: #1a1a1a;
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
        }

        .search-input-modal:focus {
            outline: none;
            border-color: rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .search-icon-modal {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #6a6a6a;
        }

        .ticket-list-container {
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 24px;
            background: rgba(255, 255, 255, 0.9);
        }

        .ticket-list-header {
            display: grid;
            grid-template-columns: 60px 200px 120px 150px 150px 80px;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.02));
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .ticket-header-cell {
            padding: 16px 12px;
            font-weight: 700;
            font-size: 12px;
            color: #1a1a1a;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-right: 1px solid rgba(0, 0, 0, 0.05);
        }

        .ticket-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .ticket-item {
            display: grid;
            grid-template-columns: 60px 200px 120px 150px 150px 80px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .ticket-item:hover {
            background: rgba(0, 0, 0, 0.02);
        }

        .ticket-item.dragging {
            opacity: 0.5;
            transform: rotate(2deg);
        }

        .ticket-cell {
            padding: 12px;
            font-size: 13px;
            color: #4a4a4a;
            border-right: 1px solid rgba(0, 0, 0, 0.05);
            display: flex;
            align-items: center;
        }

        .ticket-id {
            font-weight: 600;
            color: #1a1a1a;
        }

        .ticket-checkbox {
            width: 16px;
            height: 16px;
            accent-color: #1a1a1a;
            cursor: pointer;
        }

        /* Drop Zone Styles */
        .drop-zone {
            border: 2px dashed rgba(0, 0, 0, 0.2);
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            background: rgba(0, 0, 0, 0.01);
            transition: all 0.3s ease;
            margin-top: 24px;
        }

        .drop-zone.drag-over {
            border-color: #1a1a1a;
            background: rgba(0, 0, 0, 0.05);
            transform: scale(1.02);
        }

        .drop-zone-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .drop-icon {
            font-size: 32px;
            color: #6a6a6a;
            margin-bottom: 8px;
        }

        .drop-icon.success {
            color: #28a745;
        }

        .drop-zone p {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #4a4a4a;
        }

        .drop-zone-subtitle {
            font-size: 14px !important;
            font-weight: 400 !important;
            color: #6a6a6a !important;
        }

        .drop-zone-content.selected {
            color: #28a745;
        }

        /* Main Content Wrapper */
        .main-content-wrapper {
            display: flex;
            gap: 24px;
            min-height: 60vh;
        }

        .form-content {
            flex: 1;
            transition: all 0.3s ease;
        }

        /* Ticket Selector Panel */
        .ticket-selector-panel {
            width: 40%;
            background: rgba(255, 255, 255, 0.95);
            border-left: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 0 0 0 16px;
            overflow: hidden;
            animation: slideInRight 0.3s ease-out;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .ticket-panel-header {
            padding: 16px 20px;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.03), rgba(0, 0, 0, 0.01));
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .ticket-panel-header h3 {
            font-size: 16px;
            font-weight: 700;
            color: #1a1a1a;
            margin: 0 0 2px 0;
        }

        .ticket-count {
            font-size: 11px;
            color: #6a6a6a;
            font-weight: 500;
        }

        .close-panel-btn {
            background: none;
            border: none;
            font-size: 16px;
            color: #6a6a6a;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-panel-btn:hover {
            background: rgba(0, 0, 0, 0.05);
            color: #1a1a1a;
        }

        .ticket-search-container {
            padding: 12px 16px;
            position: relative;
        }

        .ticket-search-input {
            width: 100%;
            padding: 8px 32px 8px 10px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 6px;
            font-size: 12px;
            color: #1a1a1a;
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
        }

        .ticket-search-input:focus {
            outline: none;
            border-color: rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 1);
        }

        .ticket-search-icon {
            position: absolute;
            right: 36px;
            top: 50%;
            transform: translateY(-50%);
            color: #6a6a6a;
            font-size: 12px;
        }

        .ticket-cards-container {
            padding: 0 16px;
            max-height: calc(100vh - 200px);
            overflow-y: auto;
        }

        /* Ticket Card Styles */
        .ticket-card {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            margin-bottom: 10px;
            cursor: grab;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .ticket-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
            border-color: rgba(0, 0, 0, 0.2);
        }

        .ticket-card.dragging-card {
            opacity: 0.7;
            transform: rotate(3deg) scale(0.95);
            cursor: grabbing;
        }

        .ticket-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.02));
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .ticket-id-card {
            font-weight: 700;
            font-size: 12px;
            color: #1a1a1a;
        }

        .ticket-year {
            background: rgba(0, 0, 0, 0.1);
            color: #4a4a4a;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: 600;
        }

        .ticket-card-body {
            padding: 10px 12px;
        }

        .ticket-field {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
            font-size: 11px;
        }

        .ticket-field:last-child {
            margin-bottom: 0;
        }

        .field-label {
            color: #6a6a6a;
            font-weight: 500;
        }

        .field-value {
            color: #1a1a1a;
            font-weight: 600;
            text-align: right;
            max-width: 60%;
            word-break: break-word;
        }

        .ticket-card-footer {
            padding: 8px 12px;
            background: rgba(0, 0, 0, 0.01);
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }

        .select-ticket-btn {
            width: 100%;
            padding: 6px 10px;
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }

        .select-ticket-btn:hover {
            background: linear-gradient(135deg, #2d2d2d, #404040);
            transform: translateY(-1px);
        }

        /* Inline Drop Zone */
        .drop-zone-inline {
            margin: 12px 16px 16px 16px;
            border: 2px dashed rgba(0, 0, 0, 0.2);
            border-radius: 6px;
            padding: 16px;
            text-align: center;
            background: rgba(0, 0, 0, 0.01);
            transition: all 0.3s ease;
        }

        .drop-zone-inline.drag-over-inline {
            border-color: #1a1a1a;
            background: rgba(0, 0, 0, 0.05);
            transform: scale(1.02);
        }

        .drop-zone-content-inline {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 6px;
        }

        .drop-icon-inline {
            font-size: 20px;
            color: #6a6a6a;
        }

        .drop-icon-inline.success {
            color: #28a745;
        }

        .drop-zone-inline p {
            margin: 0;
            font-size: 12px;
            font-weight: 600;
            color: #4a4a4a;
        }

        .drop-zone-content-inline.selected {
            color: #28a745;
        }

        /* Drag over effects for ticket input */
        .form-input.drag-over-inline {
            border-color: #1a1a1a !important;
            background: rgba(0, 0, 0, 0.05) !important;
            transform: scale(1.02);
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .sidebar {
                width: 200px;
            }

            .search-input {
                width: 200px;
            }
        }

        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
            }

            .header {
                flex-direction: column;
                gap: 10px;
                padding: 15px;
            }

            .header-left,
            .header-right {
                width: 100%;
                justify-content: center;
            }

            .search-input {
                width: 100%;
                max-width: 300px;
            }

            .content-header {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }

            .action-bar {
                flex-direction: column;
                gap: 15px;
            }

            .action-left {
                flex-wrap: wrap;
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .content {
                margin: 10px;
                padding: 15px;
            }

            .data-table {
                font-size: 11px;
            }

            .data-table th,
            .data-table td {
                padding: 8px 4px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-left">
            <div class="logo">
                <span class="logo-text">HCLSoftware</span>
            </div>
            <div class="search-container">
                <input type="search" class="search-input" placeholder="Search...">
                <i class="fas fa-search search-icon"></i>
            </div>
        </div>
        <div class="header-right">
            <span class="welcome-text">Welcome - Admin - <strong>Branch 11</strong></span>
            <i class="fas fa-question-circle help-icon"></i>
        </div>
    </header>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="nav-section">
                <div class="nav-item active">
                    <span class="nav-text">Service Invoice Return</span>
                </div>
                <div class="nav-item">
                    <span class="nav-text">Service Type</span>
                </div>
                <div class="nav-item">
                    <span class="nav-text">Movement Type</span>
                </div>
            </div>

            <div class="nav-menu">
                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>HELPDESK</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>EMAILS</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>SERVICE</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>ITEMS</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>SCHEDULER</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>CRM</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>DASHBOARD</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>JOB RECEIPT</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>CONTRACT MANAGEMENT</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>DIGITAL CATALOGUE</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>REPORTS</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>ORDER MANAGEMENT</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>FIELD SERVICE</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>WARRANTY</span>
                    </div>
                </div>

                <div class="nav-group">
                    <div class="nav-group-header">
                        <i class="fas fa-chevron-down"></i>
                        <span>SALES</span>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="content">
            <!-- Content Header -->
            <div class="content-header">
                <div class="content-title">
                    <h1>Service Quotation</h1>
                </div>
                <div class="content-actions">
                    <button class="btn btn-primary" onclick="openNewQuotationModal()">
                        <i class="fas fa-plus"></i>
                        New
                    </button>
                </div>
            </div>

            <!-- Action Bar -->
            <div class="action-bar">
                <div class="action-left">
                    <button class="btn btn-secondary">
                        <i class="fas fa-search"></i>
                        Advanced Search
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-download"></i>
                        Export
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-refresh"></i>
                        Refresh
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-info-circle"></i>
                        Legend
                    </button>
                </div>
            </div>

            <!-- Data Table -->
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>View</th>
                            <th>Quotation #</th>
                            <th>Ver</th>
                            <th>Customer #</th>
                            <th>Name</th>
                            <th>Customer Complaint</th>
                            <th>Date</th>
                            <th>Model</th>
                            <th>Unit #</th>
                            <th>Serial #</th>
                            <th>Work Order #</th>
                            <th>Ticket #</th>
                            <th>Ticket Date</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><i class="fas fa-eye view-icon"></i></td>
                            <td class="quotation-link">QOT17N/2025-1</td>
                            <td>1</td>
                            <td>0100022087</td>
                            <td>Customer 1487</td>
                            <td>Axis replacement</td>
                            <td>26-Dec-2024</td>
                            <td>Model 30</td>
                            <td></td>
                            <td>Serial3898</td>
                            <td>WO17N/2025-1</td>
                            <td></td>
                            <td></td>
                            <td><span class="status approved">Approved</span></td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-eye view-icon"></i></td>
                            <td class="quotation-link">QOT17N/2024-1</td>
                            <td>1</td>
                            <td>0100024564</td>
                            <td>Customer 1673</td>
                            <td>Gear box replacement</td>
                            <td>24-Nov-2024</td>
                            <td>Model 36</td>
                            <td></td>
                            <td>Serial9911</td>
                            <td>WO17N/2024-1</td>
                            <td></td>
                            <td></td>
                            <td><span class="status approved">Approved</span></td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-eye view-icon"></i></td>
                            <td class="quotation-link">QOT17N/2024-1</td>
                            <td>1</td>
                            <td>0100024563</td>
                            <td>Customer 156-90</td>
                            <td>Oil</td>
                            <td>24-Nov-2024</td>
                            <td>Model 35</td>
                            <td></td>
                            <td>Serial5531</td>
                            <td></td>
                            <td>TKT7N/2024-1</td>
                            <td>25-Dec-2024</td>
                            <td><span class="status approved">Approved</span></td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-eye view-icon"></i></td>
                            <td class="quotation-link">QOT17N/2024-1</td>
                            <td>1</td>
                            <td>0100017358</td>
                            <td>Customer 5791</td>
                            <td>Fire testing like yesterday</td>
                            <td>25-Dec-2024</td>
                            <td>Model 18</td>
                            <td>SN147</td>
                            <td>Serial33968</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td><span class="status pending">Pending for C</span></td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-eye view-icon"></i></td>
                            <td class="quotation-link">QOT17N/2024-4</td>
                            <td>1</td>
                            <td>0100024549</td>
                            <td>Customer 156-90</td>
                            <td>test</td>
                            <td>21-Dec-2024</td>
                            <td>Model 16</td>
                            <td></td>
                            <td>Serial20940</td>
                            <td>TKT7N/2024-4</td>
                            <td>21-Dec-2024</td>
                            <td><span class="status pending">Pending for C</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="pagination-container">
                <div class="pagination-info">
                    <span>1 of 1 of 5</span>
                </div>
                <div class="pagination-controls">
                    <button class="pagination-btn" disabled>
                        <i class="fas fa-angle-double-left"></i>
                    </button>
                    <button class="pagination-btn" disabled>
                        <i class="fas fa-angle-left"></i>
                    </button>
                    <button class="pagination-btn" disabled>
                        <i class="fas fa-angle-right"></i>
                    </button>
                    <button class="pagination-btn" disabled>
                        <i class="fas fa-angle-double-right"></i>
                    </button>
                </div>
            </div>
        </main>
    </div>

    <!-- New Quotation Modal -->
    <div id="quotationModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Service Quotation</h2>
                <div class="modal-actions">
                    <button class="btn btn-secondary btn-sm" onclick="saveQuotation()">
                        <i class="fas fa-save"></i>
                        Save
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="closeModal()">
                        <i class="fas fa-times"></i>
                        Close
                    </button>
                </div>
            </div>

            <div class="modal-body">
                <!-- Form Content -->
                        <!-- Basic Information Section -->
                        <div class="form-section">
                            <div class="section-header">
                                <h3>Basic Information</h3>
                            </div>

                            <!-- Top Row with Key Fields -->
                            <div class="top-form-row">
                                <div class="form-group checkbox-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="isPartner" class="form-checkbox">
                                        <span class="checkbox-text">Is Partner?</span>
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label for="quotationNumber">Quotation #</label>
                                    <input type="text" id="quotationNumber" class="form-input" value="Auto-generated" readonly>
                                </div>
                                <div class="form-group">
                                    <label for="quotationDate">Quotation Date</label>
                                    <input type="date" id="quotationDate" class="form-input" value="2025-01-20">
                                </div>
                                <div class="form-group">
                                    <label for="status">Status</label>
                                    <select id="status" class="form-select">
                                        <option value="draft">Draft</option>
                                        <option value="pending">Pending</option>
                                        <option value="approved">Approved</option>
                                        <option value="rejected">Rejected</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Second Row -->
                            <div class="second-form-row">
                                <div class="form-group">
                                    <label for="quotationValidity">Quotation Validity</label>
                                    <input type="date" id="quotationValidity" class="form-input" value="2025-01-27">
                                </div>
                                <div class="form-group ticket-search-group">
                                    <label for="ticketNumber">Ticket #</label>
                                    <div class="ticket-input-container">
                                        <input type="text" id="ticketNumber" class="form-input" placeholder="Drag a ticket here or click search" readonly>
                                        <button type="button" class="search-ticket-btn" onclick="toggleTicketSelector()">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="button" class="btn btn-secondary" onclick="clearTicket()">
                                        <i class="fas fa-times"></i>
                                        Clear
                                    </button>
                                </div>
                            </div>
                        </div>

                <!-- Customer Details Section -->
                <div class="form-section">
                    <div class="section-header collapsible" onclick="toggleSection('customerDetails')">
                        <h3><i class="fas fa-chevron-down"></i> Customer Details</h3>
                    </div>
                    <div id="customerDetails" class="section-content">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="customerId">Customer ID</label>
                                <input type="text" id="customerId" class="form-input" placeholder="Enter Customer ID">
                            </div>
                            <div class="form-group">
                                <label for="customerName">Customer Name</label>
                                <input type="text" id="customerName" class="form-input" placeholder="Enter Customer Name">
                            </div>
                            <div class="form-group">
                                <label for="customerComplaint">Customer Complaint</label>
                                <textarea id="customerComplaint" class="form-textarea" rows="3" placeholder="Describe the customer complaint..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Asset Details Section -->
                <div class="form-section">
                    <div class="section-header collapsible" onclick="toggleSection('assetDetails')">
                        <h3><i class="fas fa-chevron-down"></i> Asset Details</h3>
                    </div>
                    <div id="assetDetails" class="section-content">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="model">Model</label>
                                <input type="text" id="model" class="form-input" placeholder="Enter Model">
                            </div>
                            <div class="form-group">
                                <label for="serialNumber">Serial Number</label>
                                <input type="text" id="serialNumber" class="form-input" placeholder="Enter Serial Number">
                            </div>
                            <div class="form-group">
                                <label for="unitNumber">Unit Number</label>
                                <input type="text" id="unitNumber" class="form-input" placeholder="Enter Unit Number">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Planning and Actuals Section -->
                <div class="form-section">
                    <div class="section-header collapsible" onclick="toggleSection('planningActuals')">
                        <h3><i class="fas fa-chevron-down"></i> Planning and Actuals</h3>
                    </div>
                    <div id="planningActuals" class="section-content">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="workOrderNumber">Work Order #</label>
                                <input type="text" id="workOrderNumber" class="form-input" placeholder="Enter Work Order Number">
                            </div>
                            <div class="form-group">
                                <label for="estimatedHours">Estimated Hours</label>
                                <input type="number" id="estimatedHours" class="form-input" placeholder="0.00" step="0.01">
                            </div>
                            <div class="form-group">
                                <label for="actualHours">Actual Hours</label>
                                <input type="number" id="actualHours" class="form-input" placeholder="0.00" step="0.01">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Component Details Section -->
                <div class="form-section">
                    <div class="section-header collapsible" onclick="toggleSection('componentDetails')">
                        <h3><i class="fas fa-chevron-down"></i> Component Details</h3>
                    </div>
                    <div id="componentDetails" class="section-content">
                        <div class="component-tabs">
                            <button class="tab-btn active" onclick="switchTab('hardware')">Hardware</button>
                            <button class="tab-btn" onclick="switchTab('parts')">Parts Suggested List</button>
                            <button class="tab-btn" onclick="switchTab('labor')">Labor and Misc. Details</button>
                            <button class="tab-btn" onclick="switchTab('charges')">Charges Part</button>
                            <button class="tab-btn" onclick="switchTab('attachment')">Attachment Details</button>
                            <button class="tab-btn" onclick="switchTab('summary')">Summary</button>
                        </div>

                        <div id="hardware" class="tab-content active">
                            <div class="status-legend">
                                <span class="legend-item">
                                    <span class="legend-color" style="background: #28a745;"></span>
                                    In Progress
                                </span>
                                <span class="legend-item">
                                    <span class="legend-color" style="background: #ffc107;"></span>
                                    Approved
                                </span>
                                <span class="legend-item">
                                    <span class="legend-color" style="background: #dc3545;"></span>
                                    Rejected
                                </span>
                            </div>

                            <div class="component-table-container">
                                <table class="component-table">
                                    <thead>
                                        <tr>
                                            <th>Quotation Ref #</th>
                                            <th>Quotation Date</th>
                                            <th>Ticket Ref #</th>
                                            <th>Job #</th>
                                            <th>Changed To *</th>
                                            <th>Type *</th>
                                            <th>Authorization Code</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><input type="text" class="table-input" placeholder="REF001"></td>
                                            <td><input type="date" class="table-input"></td>
                                            <td><input type="text" class="table-input" placeholder="TKT001"></td>
                                            <td><input type="text" class="table-input" placeholder="JOB001"></td>
                                            <td>
                                                <select class="table-select">
                                                    <option>Invoice</option>
                                                    <option>Quote</option>
                                                    <option>Order</option>
                                                </select>
                                            </td>
                                            <td>
                                                <select class="table-select">
                                                    <option>Select</option>
                                                    <option>Hardware</option>
                                                    <option>Software</option>
                                                    <option>Service</option>
                                                </select>
                                            </td>
                                            <td><input type="text" class="table-input" placeholder="AUTH001"></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div id="parts" class="tab-content">
                            <p>Parts Suggested List content will be displayed here...</p>
                        </div>

                        <div id="labor" class="tab-content">
                            <p>Labor and Misc. Details content will be displayed here...</p>
                        </div>

                        <div id="charges" class="tab-content">
                            <p>Charges Part content will be displayed here...</p>
                        </div>

                        <div id="attachment" class="tab-content">
                            <p>Attachment Details content will be displayed here...</p>
                        </div>

                        <div id="summary" class="tab-content">
                            <p>Summary content will be displayed here...</p>
                        </div>
                    </div>
                </div>

                <!-- Additional Details Section -->
                <div class="form-section">
                    <div class="section-header collapsible" onclick="toggleSection('additionalDetails')">
                        <h3><i class="fas fa-chevron-down"></i> Other Detail</h3>
                    </div>
                    <div id="additionalDetails" class="section-content">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="causeOfFailure">Cause Of Failure</label>
                                <textarea id="causeOfFailure" class="form-textarea" rows="3" placeholder="Describe the cause of failure..."></textarea>
                            </div>
                            <div class="form-group">
                                <label for="proposedSolution">Proposed Solution</label>
                                <textarea id="proposedSolution" class="form-textarea" rows="3" placeholder="Describe the proposed solution..."></textarea>
                            </div>
                            <div class="form-group">
                                <label for="remarks">Remarks</label>
                                <textarea id="remarks" class="form-textarea" rows="3" placeholder="Additional remarks..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions Section -->
                <div class="form-section">
                    <div class="section-header">
                        <h3>Actions</h3>
                    </div>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="currentStep">Current Step</label>
                            <select id="currentStep" class="form-select">
                                <option>Quotation Initiated</option>
                                <option>Under Review</option>
                                <option>Approved</option>
                                <option>Completed</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="actionRemarks">Action Remarks</label>
                            <textarea id="actionRemarks" class="form-textarea" rows="2" placeholder="Action remarks..."></textarea>
                        </div>
                        <div class="form-group">
                            <label for="saveAsDraft">Save as Draft</label>
                            <select id="saveAsDraft" class="form-select">
                                <option>Yes</option>
                                <option>No</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="assignedTo">Assigned To</label>
                            <select id="assignedTo" class="form-select">
                                <option>Admin</option>
                                <option>Technician 1</option>
                                <option>Technician 2</option>
                                <option>Manager</option>
                            </select>
                        </div>
                        </div>
            </div>

            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                <button class="btn btn-primary" onclick="saveQuotation()">Save Quotation</button>
            </div>
        </div>
    </div>

    <!-- Separate Ticket Panel Modal -->
    <div id="ticketPanelModal" class="ticket-panel-modal">
        <div class="ticket-panel-header">
            <div>
                <h3>Available Tickets</h3>
                <div class="ticket-count" id="ticketCount">9 tickets available</div>
            </div>
            <button class="close-panel-btn" onclick="closeTicketSelector()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="ticket-search-container">
            <input type="text" id="ticketSearchInput" class="ticket-search-input" placeholder="Search tickets..." onkeyup="filterTicketCards()">
            <i class="fas fa-search ticket-search-icon"></i>
        </div>

        <div class="ticket-cards-container" id="ticketCardsContainer">
            <!-- Ticket cards will be populated here -->
        </div>

        <div class="drop-zone-inline" id="dropZoneInline">
            <div class="drop-zone-content-inline">
                <i class="fas fa-hand-paper drop-icon-inline"></i>
                <p>Drop ticket here</p>
            </div>
        </div>
    </div>

    <!-- Ticket Selector Modal -->
    <div id="ticketModal" class="modal ticket-modal">
        <div class="ticket-modal-content">
            <div class="ticket-modal-header">
                <h2>Select Ticket</h2>
                <button class="close-btn" onclick="closeTicketModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="ticket-modal-body">
                <div class="ticket-search-bar">
                    <input type="text" id="ticketSearchInput" class="search-input-modal" placeholder="Search tickets..." onkeyup="filterTickets()">
                    <i class="fas fa-search search-icon-modal"></i>
                </div>

                <div class="ticket-list-container">
                    <div class="ticket-list-header">
                        <div class="ticket-header-cell">Action</div>
                        <div class="ticket-header-cell">Ticket #</div>
                        <div class="ticket-header-cell">Serial #</div>
                        <div class="ticket-header-cell">Customer Account #</div>
                        <div class="ticket-header-cell">Name</div>
                        <div class="ticket-header-cell">Fin. Year</div>
                    </div>

                    <div class="ticket-list" id="ticketList">
                        <!-- Ticket items will be populated here -->
                    </div>
                </div>

                <div class="drop-zone" id="dropZone">
                    <div class="drop-zone-content">
                        <i class="fas fa-hand-paper drop-icon"></i>
                        <p>Drag and drop a ticket here to select</p>
                        <p class="drop-zone-subtitle">or click on a ticket to select</p>
                    </div>
                </div>
            </div>

            <div class="ticket-modal-footer">
                <button class="btn btn-secondary" onclick="closeTicketModal()">Cancel</button>
                <button class="btn btn-primary" onclick="confirmTicketSelection()" id="confirmTicketBtn" disabled>Select Ticket</button>
            </div>
        </div>
    </div>

    <script>
        // Sample ticket data
        const ticketData = [
            { id: 'TI/177N/2/2025-1', serial: '', customerAccount: '**********', name: 'Customer 100-(K)', finYear: '2025' },
            { id: 'TI/177N/1/2025-1', serial: '', customerAccount: '**********', name: 'Customer 100-(K)', finYear: '2025' },
            { id: 'TI/177N/29/2024-1', serial: 'Serial21364', customerAccount: '**********', name: 'Customer 1937', finYear: '2024' },
            { id: 'TI/177N/11/2024-1', serial: 'Serial21364', customerAccount: '**********', name: 'Customer 1937', finYear: '2024' },
            { id: 'TI/177N/173/2023-1', serial: 'Serial24537', customerAccount: '**********', name: 'Customer 12486', finYear: '2023' },
            { id: 'TI/177N/170/2023-1', serial: 'Serial44091', customerAccount: '**********', name: 'Customer 5446', finYear: '2023' },
            { id: 'TI/177N/169/2023-1', serial: 'Serial23', customerAccount: '**********', name: 'Customer 6973', finYear: '2023' },
            { id: 'TI/177N/155/2023-1', serial: 'Serial1910', customerAccount: '**********', name: 'Customer 4559', finYear: '2023' },
            { id: 'TI/177N/137/2023-4', serial: 'Serial34064', customerAccount: '**********', name: 'Customer 4559', finYear: '2023' }
        ];

        let selectedTicket = null;

        function openNewQuotationModal() {
            document.getElementById('quotationModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            document.getElementById('quotationModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        function saveQuotation() {
            // Add save functionality here
            alert('Quotation saved successfully!');
            closeModal();
        }

        function toggleTicketSelector() {
            const panel = document.getElementById('ticketPanelModal');
            const modalContent = document.querySelector('.modal-content');
            const modal = document.querySelector('.modal');

            if (panel.style.display === 'none' || panel.style.display === '') {
                panel.style.display = 'block';
                modalContent.classList.add('with-ticket-panel');
                modal.style.justifyContent = 'flex-start';
                modal.style.alignItems = 'center';
                modal.style.paddingLeft = '20px';

                populateTicketCards();
                setupInlineDropZone();
            } else {
                closeTicketSelector();
            }
        }

        function closeTicketSelector() {
            const panel = document.getElementById('ticketPanelModal');
            const modalContent = document.querySelector('.modal-content');
            const modal = document.querySelector('.modal');

            panel.style.display = 'none';
            modalContent.classList.remove('with-ticket-panel');
            modal.style.justifyContent = 'center';
            modal.style.alignItems = 'center';
            modal.style.paddingLeft = '20px';
        }

        function clearTicket() {
            document.getElementById('ticketNumber').value = '';
            selectedTicket = null;
            // Clear any auto-filled fields
            document.getElementById('customerId').value = '';
            document.getElementById('customerName').value = '';
        }

        function populateTicketCards() {
            const container = document.getElementById('ticketCardsContainer');
            container.innerHTML = '';

            ticketData.forEach((ticket, index) => {
                const ticketCard = document.createElement('div');
                ticketCard.className = 'ticket-card';
                ticketCard.draggable = true;
                ticketCard.dataset.ticketId = ticket.id;
                ticketCard.innerHTML = `
                    <div class="ticket-card-header">
                        <div class="ticket-id-card">${ticket.id}</div>
                        <div class="ticket-year">${ticket.finYear}</div>
                    </div>
                    <div class="ticket-card-body">
                        <div class="ticket-field">
                            <span class="field-label">Serial #:</span>
                            <span class="field-value">${ticket.serial || 'N/A'}</span>
                        </div>
                        <div class="ticket-field">
                            <span class="field-label">Customer Account:</span>
                            <span class="field-value">${ticket.customerAccount}</span>
                        </div>
                        <div class="ticket-field">
                            <span class="field-label">Name:</span>
                            <span class="field-value">${ticket.name}</span>
                        </div>
                    </div>
                    <div class="ticket-card-footer">
                        <button class="select-ticket-btn" onclick="selectTicketFromCard('${ticket.id}')">
                            <i class="fas fa-check"></i> Select
                        </button>
                    </div>
                `;

                // Add drag event listeners
                ticketCard.addEventListener('dragstart', handleCardDragStart);
                ticketCard.addEventListener('dragend', handleCardDragEnd);

                container.appendChild(ticketCard);
            });

            // Update ticket count
            document.getElementById('ticketCount').textContent = `${ticketData.length} tickets available`;
        }

        function selectTicketFromCard(ticketId) {
            selectedTicket = ticketData.find(t => t.id === ticketId);
            if (selectedTicket) {
                fillTicketData(selectedTicket);
                closeTicketSelector();
            }
        }

        function fillTicketData(ticket) {
            document.getElementById('ticketNumber').value = ticket.id;

            // Auto-fill customer details
            document.getElementById('customerId').value = ticket.customerAccount;
            document.getElementById('customerName').value = ticket.name;

            // Show success feedback
            const ticketInput = document.getElementById('ticketNumber');
            ticketInput.style.background = 'rgba(40, 167, 69, 0.1)';
            ticketInput.style.borderColor = '#28a745';

            setTimeout(() => {
                ticketInput.style.background = '';
                ticketInput.style.borderColor = '';
            }, 2000);
        }

        function filterTicketCards() {
            const searchTerm = document.getElementById('ticketSearchInput').value.toLowerCase();
            const ticketCards = document.querySelectorAll('.ticket-card');
            let visibleCount = 0;

            ticketCards.forEach(card => {
                const text = card.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    card.style.display = 'block';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });

            document.getElementById('ticketCount').textContent = `${visibleCount} tickets available`;
        }

        // Drag and Drop functionality for cards
        function handleCardDragStart(e) {
            e.dataTransfer.setData('text/plain', e.target.dataset.ticketId);
            e.target.classList.add('dragging-card');
        }

        function handleCardDragEnd(e) {
            e.target.classList.remove('dragging-card');
        }

        function setupInlineDropZone() {
            const dropZone = document.getElementById('dropZoneInline');
            const ticketInput = document.getElementById('ticketNumber');

            // Make ticket input a drop zone too
            [dropZone, ticketInput].forEach(element => {
                element.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    element.classList.add('drag-over-inline');
                });

                element.addEventListener('dragleave', function(e) {
                    if (!element.contains(e.relatedTarget)) {
                        element.classList.remove('drag-over-inline');
                    }
                });

                element.addEventListener('drop', function(e) {
                    e.preventDefault();
                    element.classList.remove('drag-over-inline');

                    const ticketId = e.dataTransfer.getData('text/plain');
                    selectedTicket = ticketData.find(t => t.id === ticketId);

                    if (selectedTicket) {
                        fillTicketData(selectedTicket);

                        // Visual feedback for drop zone
                        if (element === dropZone) {
                            dropZone.innerHTML = `
                                <div class="drop-zone-content-inline selected">
                                    <i class="fas fa-check-circle drop-icon-inline success"></i>
                                    <p><strong>Ticket Selected!</strong></p>
                                </div>
                            `;

                            setTimeout(() => {
                                dropZone.innerHTML = `
                                    <div class="drop-zone-content-inline">
                                        <i class="fas fa-hand-paper drop-icon-inline"></i>
                                        <p>Drop ticket here</p>
                                    </div>
                                `;
                            }, 2000);
                        }
                    }
                });
            });
        }

        function populateTicketList() {
            const ticketList = document.getElementById('ticketList');
            ticketList.innerHTML = '';

            ticketData.forEach((ticket, index) => {
                const ticketItem = document.createElement('div');
                ticketItem.className = 'ticket-item';
                ticketItem.draggable = true;
                ticketItem.dataset.ticketId = ticket.id;
                ticketItem.innerHTML = `
                    <div class="ticket-cell">
                        <input type="checkbox" class="ticket-checkbox" onchange="selectTicket('${ticket.id}', this)">
                    </div>
                    <div class="ticket-cell ticket-id">${ticket.id}</div>
                    <div class="ticket-cell">${ticket.serial}</div>
                    <div class="ticket-cell">${ticket.customerAccount}</div>
                    <div class="ticket-cell">${ticket.name}</div>
                    <div class="ticket-cell">${ticket.finYear}</div>
                `;

                // Add drag event listeners
                ticketItem.addEventListener('dragstart', handleDragStart);
                ticketItem.addEventListener('click', () => selectTicketByClick(ticket.id));

                ticketList.appendChild(ticketItem);
            });
        }

        function selectTicket(ticketId, checkbox) {
            // Uncheck all other checkboxes
            const checkboxes = document.querySelectorAll('.ticket-checkbox');
            checkboxes.forEach(cb => {
                if (cb !== checkbox) cb.checked = false;
            });

            if (checkbox.checked) {
                selectedTicket = ticketData.find(t => t.id === ticketId);
                document.getElementById('confirmTicketBtn').disabled = false;
            } else {
                selectedTicket = null;
                document.getElementById('confirmTicketBtn').disabled = true;
            }
        }

        function selectTicketByClick(ticketId) {
            const checkbox = document.querySelector(`input[onchange*="${ticketId}"]`);
            checkbox.checked = !checkbox.checked;
            selectTicket(ticketId, checkbox);
        }

        function confirmTicketSelection() {
            if (selectedTicket) {
                document.getElementById('ticketNumber').value = selectedTicket.id;
                closeTicketModal();
            }
        }

        function filterTickets() {
            const searchTerm = document.getElementById('ticketSearchInput').value.toLowerCase();
            const ticketItems = document.querySelectorAll('.ticket-item');

            ticketItems.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // Drag and Drop functionality
        function handleDragStart(e) {
            e.dataTransfer.setData('text/plain', e.target.dataset.ticketId);
            e.target.classList.add('dragging');
        }

        // Setup drop zone
        document.addEventListener('DOMContentLoaded', function() {
            const dropZone = document.getElementById('dropZone');

            dropZone.addEventListener('dragover', function(e) {
                e.preventDefault();
                dropZone.classList.add('drag-over');
            });

            dropZone.addEventListener('dragleave', function(e) {
                if (!dropZone.contains(e.relatedTarget)) {
                    dropZone.classList.remove('drag-over');
                }
            });

            dropZone.addEventListener('drop', function(e) {
                e.preventDefault();
                dropZone.classList.remove('drag-over');

                const ticketId = e.dataTransfer.getData('text/plain');
                selectedTicket = ticketData.find(t => t.id === ticketId);

                if (selectedTicket) {
                    // Update UI to show selection
                    const checkbox = document.querySelector(`input[onchange*="${ticketId}"]`);
                    if (checkbox) {
                        // Uncheck all other checkboxes
                        document.querySelectorAll('.ticket-checkbox').forEach(cb => cb.checked = false);
                        checkbox.checked = true;
                    }

                    document.getElementById('confirmTicketBtn').disabled = false;

                    // Visual feedback
                    dropZone.innerHTML = `
                        <div class="drop-zone-content selected">
                            <i class="fas fa-check-circle drop-icon success"></i>
                            <p><strong>${selectedTicket.id}</strong> selected</p>
                            <p class="drop-zone-subtitle">${selectedTicket.name} - ${selectedTicket.customerAccount}</p>
                        </div>
                    `;

                    setTimeout(() => {
                        dropZone.innerHTML = `
                            <div class="drop-zone-content">
                                <i class="fas fa-hand-paper drop-icon"></i>
                                <p>Drag and drop a ticket here to select</p>
                                <p class="drop-zone-subtitle">or click on a ticket to select</p>
                            </div>
                        `;
                    }, 2000);
                }
            });

            // Remove dragging class when drag ends
            document.addEventListener('dragend', function(e) {
                e.target.classList.remove('dragging');
            });
        });

        function toggleSection(sectionId) {
            const section = document.getElementById(sectionId);
            const header = section.previousElementSibling;
            const icon = header.querySelector('i');

            if (section.style.display === 'none') {
                section.style.display = 'block';
                icon.classList.remove('fa-chevron-right');
                icon.classList.add('fa-chevron-down');
            } else {
                section.style.display = 'none';
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-right');
            }
        }

        function switchTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // Remove active class from all tab buttons
            const tabBtns = document.querySelectorAll('.tab-btn');
            tabBtns.forEach(btn => btn.classList.remove('active'));

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked tab button
            event.target.classList.add('active');
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('quotationModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
