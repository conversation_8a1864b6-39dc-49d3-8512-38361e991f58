/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    background-color: #f8f9fa;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    color: white;
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.logo-text {
    font-size: 18px;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.search-container {
    position: relative;
}

.search-input {
    width: 300px;
    padding: 8px 40px 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 14px;
    transition: all 0.3s ease;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.search-input:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

.search-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.welcome-text {
    font-size: 13px;
    font-weight: 400;
}

.help-icon {
    font-size: 16px;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.help-icon:hover {
    opacity: 1;
}

/* Main Container */
.main-container {
    display: flex;
    min-height: calc(100vh - 60px);
}

/* Sidebar Styles */
.sidebar {
    width: 250px;
    background: white;
    border-right: 1px solid #e5e7eb;
    overflow-y: auto;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05);
}

.nav-section {
    padding: 15px;
    border-bottom: 1px solid #e5e7eb;
    background: #f8f9fa;
}

.nav-item {
    padding: 8px 12px;
    margin: 2px 0;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-item:hover {
    background: #e5e7eb;
}

.nav-item.active {
    background: #3b82f6;
    color: white;
}

.nav-menu {
    padding: 10px 0;
}

.nav-group {
    margin: 2px 0;
}

.nav-group-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.nav-group-header:hover {
    background: #f3f4f6;
    color: #374151;
}

.nav-group-header i {
    font-size: 10px;
    transition: transform 0.3s ease;
}

/* Content Styles */
.content {
    flex: 1;
    padding: 20px;
    background: white;
    margin: 15px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e5e7eb;
}

.content-title h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-primary {
    background: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #e5e7eb;
    border-color: #9ca3af;
}

/* Action Bar */
.action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.action-left {
    display: flex;
    gap: 10px;
}

/* Table Styles */
.table-container {
    overflow-x: auto;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 20px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.data-table th {
    background: #f8f9fa;
    padding: 12px 8px;
    text-align: left;
    font-weight: 600;
    font-size: 12px;
    color: #374151;
    border-bottom: 2px solid #e5e7eb;
    white-space: nowrap;
}

.data-table td {
    padding: 10px 8px;
    border-bottom: 1px solid #f3f4f6;
    font-size: 12px;
    color: #4b5563;
}

.data-table tr:hover {
    background: #f8f9fa;
}

.view-icon {
    color: #6b7280;
    cursor: pointer;
    transition: color 0.3s ease;
}

.view-icon:hover {
    color: #3b82f6;
}

.quotation-link {
    color: #3b82f6;
    cursor: pointer;
    font-weight: 500;
}

.quotation-link:hover {
    text-decoration: underline;
}

/* Status Styles */
.status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status.approved {
    background: #dcfce7;
    color: #166534;
}

.status.pending {
    background: #fef3c7;
    color: #92400e;
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-top: 1px solid #e5e7eb;
}

.pagination-info {
    font-size: 13px;
    color: #6b7280;
}

.pagination-controls {
    display: flex;
    gap: 5px;
}

.pagination-btn {
    width: 32px;
    height: 32px;
    border: 1px solid #d1d5db;
    background: white;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
    background: #f3f4f6;
    border-color: #9ca3af;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        width: 200px;
    }
    
    .search-input {
        width: 200px;
    }
}

@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
    }
    
    .header {
        flex-direction: column;
        gap: 10px;
        padding: 15px;
    }
    
    .header-left,
    .header-right {
        width: 100%;
        justify-content: center;
    }
    
    .search-input {
        width: 100%;
        max-width: 300px;
    }
    
    .content-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .action-bar {
        flex-direction: column;
        gap: 15px;
    }
    
    .action-left {
        flex-wrap: wrap;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .content {
        margin: 10px;
        padding: 15px;
    }
    
    .data-table {
        font-size: 11px;
    }
    
    .data-table th,
    .data-table td {
        padding: 8px 4px;
    }
}
